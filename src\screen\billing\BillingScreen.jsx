import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Dimensions,
    Modal,
    Platform,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';

import { useNavigation } from '@react-navigation/native';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import { fetchBillsByDateNew } from '../../apiHandling/BillingAPI/fetchBillsAPI';
import { fetchBillDetails } from '../../apiHandling/BillingAPI/fetchBillDetailsAPI';
import { fetchTripList } from '../../apiHandling/BillingAPI/tripAPI';
import { fetchTimeSlots } from '../../apiHandling/BillingAPI/timeSlotAPI';

// api handling

//customer import
import { customerNameSearchAPI } from '../../apiHandling/BillingAPI/customerNameSearchAPI';
import { customerMobileSearchAPI } from '../../apiHandling/BillingAPI/customerMobileSearchAPI';

//item search imports
import { itemCategoryAPI } from '../../apiHandling/BillingAPI/itemCategoryAPI';
import { itemNameAPI } from '../../apiHandling/BillingAPI/itemNameAPI';
import { itemDetailsAPI } from '../../apiHandling/BillingAPI/itemDetailsAPI';
import { itemPriceAPI } from '../../apiHandling/BillingAPI/itemPriceAPI';

import AsyncStorage from '@react-native-async-storage/async-storage';


const BillingScreen = ({ }) => {


    const [billModalVisible, setBillModalVisible] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [salesList, setSalesList] = useState([]);
    const [filteredSales, setFilteredSales] = useState([]);
    const [selectedDate, setSelectedDate] = useState(null);
    const [showDatePicker, setShowDatePicker] = useState(false);

    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [showFromDatePicker, setShowFromDatePicker] = useState(false);
    const [showToDatePicker, setShowToDatePicker] = useState(false);




    // Function to open modal & fetch initial bills & date
    const openBillModal = async () => {
        setLoading(true);
        const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        try {
            const businessDate = await fetchBusinessDay(bearerToken, loginBranchID); // format: DD-MM-YYYY
            const [day, month, year] = businessDate.split('-');
            const from = new Date(`${year}-${month}-${day}`);
            const to = new Date(from);
            to.setDate(to.getDate() + 1); // next day

            setFromDate(from);
            setToDate(to);

            const fromApiDate = `${year}${month}${day}`;
            const toApiDate = `${to.getFullYear()}${String(to.getMonth() + 1).padStart(2, '0')}${String(to.getDate()).padStart(2, '0')}`;

            const bills = await fetchBillsByDateNew(loginBranchID, 'PL', fromApiDate, toApiDate, bearerToken);
            // Sort bills by SaleId in increasing order
            const sortedBills = bills.sort((a, b) => {
                const saleIdA = parseInt(a.SaleId) || 0;
                const saleIdB = parseInt(b.SaleId) || 0;
                return saleIdA - saleIdB;
            });
            setSalesList(sortedBills);
            setFilteredSales(sortedBills);
            setBillModalVisible(true);
        } catch (err) {
            console.error('Error fetching bills:', err);
        }
        setLoading(false);
    };


    // Fetch bills when date changes
    useEffect(() => {
        const fetchBillsBySelectedDates = async () => {
            if (!fromDate || !toDate) return;
             const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

            setLoading(true);
            try {
                const fromApiDate = `${fromDate.getFullYear()}${String(fromDate.getMonth() + 1).padStart(2, '0')}${String(fromDate.getDate()).padStart(2, '0')}`;
                const toApiDate = `${toDate.getFullYear()}${String(toDate.getMonth() + 1).padStart(2, '0')}${String(toDate.getDate()).padStart(2, '0')}`;

                const bills = await fetchBillsByDateNew(loginBranchID, 'PL', fromApiDate, toApiDate, bearerToken);
                // Sort bills by SaleId in increasing order
                const sortedBills = bills.sort((a, b) => {
                    const saleIdA = parseInt(a.SaleId) || 0;
                    const saleIdB = parseInt(b.SaleId) || 0;
                    return saleIdA - saleIdB;
                });
                setSearchQuery('');
                setSalesList(sortedBills);
                setFilteredSales(sortedBills);
            } catch (err) {
                console.error('Error fetching bills by selected date:', err);
            }
            setLoading(false);
        };

        fetchBillsBySelectedDates();
    }, [fromDate, toDate]);

    // Fetch time slots when component loads
    useEffect(() => {
        fetchTimeSlotsData();
    }, []);

const handleBillSelect = async (saleId) => {
  try {
    const bearerToken = await AsyncStorage.getItem('authToken');
    const selectedBranch = await AsyncStorage.getItem('selectedBranch');
    const parsedBranch = JSON.parse(selectedBranch);
    const loginBranchID = parsedBranch.BranchId;

    const res = await fetchBillDetails(loginBranchID, saleId, bearerToken);

    if (res?.saleHeader?.length && res?.customerInfo?.length) {
      const saleHeader = res.saleHeader[0];
      const saleDetails = res.saleDetails || [];
      const customerInfo = res.customerInfo[0];

      const deliveryAddress =
        Array.isArray(res.deliveryAddress) && res.deliveryAddress.length > 0
          ? res.deliveryAddress[0]
          : {};

      const billingDetails =
        Array.isArray(res.billingDetails) && res.billingDetails.length > 0
          ? res.billingDetails[0]
          : {};

      // 🔹 Fetch Branch Details using BranchId from saleHeader
      const branchIdFromSale = saleHeader.BranchId;
      const branchDetailsResponse = await axios.get(
        `https://retailuat.abisibg.com/api/v1/branchdetails`,
        {
          params: { Branchid: branchIdFromSale },
          headers: {
            Authorization: `Bearer ${bearerToken}`,
          },
        }
      );

      const branchData = branchDetailsResponse?.data?.[0] || {};

      const billData = {
        saleID: saleHeader.SaleId ?? '',
        currentTimeStamp: saleHeader.BusinessDate ?? '',
        itemTableDetails: saleDetails,

        customerID: customerInfo.CustID ?? '',
        customerName: customerInfo.CustomerName ?? '',
        customerPhoneNumber: customerInfo.Mobile ?? '',

        customerAddress: deliveryAddress.CustomerAddress ?? '',
        customerPlace: deliveryAddress.PlaceName ?? '',
        customerCity: deliveryAddress.CityName ?? '',
        customerState: deliveryAddress.StateName ?? '',
        customerPincode: deliveryAddress.PINCODE ?? '',

        deliveryCharges: saleHeader.DeliveryCharge ?? 0,
        selectedGatewayName: billingDetails.PaymentGatewayName ?? '',
        selectedCustomerType: saleHeader.CustomerType ?? '',

        taxAmount: saleHeader.TaxAmount?.toString() ?? '0',
        discountAmount: saleHeader.DiscountAmount?.toString() ?? '0',
        totalAmount: saleHeader.TotalAmount?.toString() ?? '0',
        roundOffAmount: saleHeader.RoundOffAmount?.toString() ?? '0',

        printDateTime: new Date().toLocaleString(),

        // 🏷️ Branch Details from separate API
        branchId: branchData.BranchId ?? '',
        branchName: branchData.BranchName ?? '',
        areaName: branchData.AreaName ?? '',
        gstNumber: branchData.GSTNumber ?? '',
        foodLicenseNumber: branchData.FoodLicenseNumber ?? '',
        branchPincode: branchData.PINCODE ?? '',
      };

      setBillModalVisible(false);
      navigation.navigate('ViewBillScreen', billData);
    } else {
      setBillModalVisible(false);
      Alert.alert('Error', 'Failed to load bill data.');
    }
  } catch (error) {
    console.error('Error in handleBillSelect:', error);
    Alert.alert('Error', 'Something went wrong while fetching bill details.');
    setBillModalVisible(false);
  }
};





    const navigation = useNavigation();
    // new, save, cancel
    const [selectedScrollOption, setSelectedScrollOption] = useState(null);

    // retail, horeca & pos, web
    const [selectedSaleType, setSelectedSaleType] = useState('RETAIL');
    const [selectedBusinessChannel, setSelectedBusinessChannel] = useState('POS');


    // customer section
    const [customerDialogVisible, setCustomerDialogVisible] = useState(false);
    const [customerList, setCustomerList] = useState([]);
    const [loading, setLoading] = useState(false);

    const [customerId, setCustomerId] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [customerMobile, setCustomerMobile] = useState('');

    // delivery mode and trip section
    const [selectedDeliveryMode, setSelectedDeliveryMode] = useState('Takeaway');
    const [selectedTripId, setSelectedTripId] = useState('');
    const [tripList, setTripList] = useState([]);
    const [tripLoading, setTripLoading] = useState(false);

    // time slot section
    const [selectedTimeSlotId, setSelectedTimeSlotId] = useState('');
    const [timeSlotList, setTimeSlotList] = useState([]);
    const [timeSlotLoading, setTimeSlotLoading] = useState(false);

    // field locking state
    const [fieldsLocked, setFieldsLocked] = useState(false);

    // refs for auto-focus
    const nosInputRef = useRef(null);
    const weightInputRef = useRef(null);

    const fetchCustomers = async (customerMobile) => {
        setLoading(true);
        setCustomerList([]);
        let result;
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;

        if (customerMobile.length === 10) {
            result = await customerMobileSearchAPI(loginBranchID, customerMobile, bearerToken);
        } else {
            result = await customerNameSearchAPI(loginBranchID, customerName,'RT', bearerToken);
        }

        if (result.success) {
            setCustomerList(result.customers);
            setCustomerDialogVisible(true);
        } else {
            Alert.alert("Error", result.message);
        }

        setLoading(false);
    };

    const handleCustomerSelect = (customer) => {
        setCustomerDialogVisible(false);
        setCustomerId(customer.customerCode);
        setCustomerName(customer.customerName);
        setCustomerMobile(customer.mobile);
    };

    // Function to create a new customer
    const createCustomer = async (customerName, customerMobile) => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;
               const userDetails = await AsyncStorage.getItem('userData');
        const user = JSON.parse(userDetails);
       const loginUserID = user.userId;

            // Create current date and time in IST format
            const now = new Date();
            const istOffset = 5.5 * 60 * 60 * 1000; // IST is UTC+5:30
            const istTime = new Date(now.getTime() + istOffset);
            const createdDate = istTime.toISOString();

            const customerData = {
                custID: "",
                customerCode: "",
                title: "",
                customerName: customerName,
                mobile: customerMobile,
                email: "",
                branchID: loginBranchID,
                addressID: "",
                isDafault: true,
                isLastDelvAddress: true,
                houseNo: "",
                building: "",
                customerAddress: "",
                landmark: "",
                cityCode: "",
                placeCode: "",
                isDistanceEdited: true,
                kmDistance: 0,
                placeName: "",
                pinCode: "",
                altPhone: "",
                createdUserId: loginUserID,
                createdDate: createdDate,
                modifiedUserId: "",
                modifiedDate: "1753-01-01T00:00:00.000Z",
                deletedUserId: "",
                deletedDate: "1753-01-01T00:00:00.000Z"
            };

            console.log('Customer Data to be sent:', customerData);

            const response = await axios.post(
                'https://retailUAT.abisaio.com:9001/api/POSCustomer',
                customerData,
                {
                    headers: {
                        'Authorization': `Bearer ${bearerToken}`,
                        'Content-Type': 'application/json'
                    }
                }
            );

            if (response.status === 200 || response.status === 201) {
                return { success: true };
            } else {
                return { success: false, message: 'Failed to create customer' };
            }
        } catch (error) {
            console.error('Error creating customer:', error);
            return { success: false, message: error.message || 'Failed to create customer' };
        }
    };

    // Function to fetch customer by phone number after creation
    const fetchCustomerByPhone = async (customerMobile) => {
        try {
            const bearerToken = await AsyncStorage.getItem('authToken');
            const selectedBranch = await AsyncStorage.getItem('selectedBranch');
            const parsedBranch = JSON.parse(selectedBranch);
            const loginBranchID = parsedBranch.BranchId;

            const result = await customerMobileSearchAPI(loginBranchID, customerMobile, bearerToken);

            if (result.success && result.customers.length > 0) {
                const customer = result.customers[0];
                setCustomerId(customer.customerCode);
                setCustomerName(customer.customerName);
                setCustomerMobile(customer.mobile);
                return { success: true, customer };
            } else {
                return { success: false, message: 'Customer not found after creation' };
            }
        } catch (error) {
            console.error('Error fetching customer by phone:', error);
            return { success: false, message: error.message || 'Failed to fetch customer' };
        }
    };

    // Function to fetch trips when delivery mode is Home delivery
    const fetchTrips = async () => {
        setTripLoading(true);
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        try {
            const businessDate = await fetchBusinessDay(bearerToken, loginBranchID);
            const [day, month, year] = businessDate.split('-');
            const businessDateFormatted = `${year}${month}${day}`;

            // Calculate next business date (business date + 1 day)
            const nextDate = new Date(`${year}-${month}-${day}`);
            nextDate.setDate(nextDate.getDate() + 1);
            const nextDay = String(nextDate.getDate()).padStart(2, '0');
            const nextMonth = String(nextDate.getMonth() + 1).padStart(2, '0');
            const nextYear = nextDate.getFullYear();
            const nextBusinessDate = `${nextYear}${nextMonth}${nextDay}`;

            const result = await fetchTripList(bearerToken, loginBranchID, businessDateFormatted, nextBusinessDate);

            if (result.success) {
                setTripList(result.trips);
            } else {
                console.error('Failed to fetch trips:', result.message);
                setTripList([]);
            }
        } catch (error) {
            console.error('Error fetching trips:', error);
            setTripList([]);
        }
        setTripLoading(false);
    };

    // Function to fetch time slots
    const fetchTimeSlotsData = async () => {
        setTimeSlotLoading(true);
        try {
            const result = await fetchTimeSlots(branchId, bearerToken);
            if (result.success) {
                setTimeSlotList(result.timeSlots);
            } else {
                console.error('Failed to fetch time slots:', result.message);
                setTimeSlotList([]);
            }
        } catch (error) {
            console.error('Error fetching time slots:', error);
            setTimeSlotList([]);
        } finally {
            setTimeSlotLoading(false);
        }
    };

    // Handle delivery mode change
    const handleDeliveryModeChange = (mode) => {
        setSelectedDeliveryMode(mode);
        setSelectedTripId(''); // Reset trip selection

        if (mode === 'Home delivery') {
            fetchTrips();
        } else {
            setTripList([]);
        }
    };


    // item search

    const [selectedItemCategory, setItemCategory] = useState('Category');
    const [selectedItemCategoryId, setItemCategoryId] = useState('');
    const [selectedItemName, setItemName] = useState('Item Type');
    const [selectedItemId, setItemId] = useState('');
    const [selectedItemBatchNumber, setBatchNumber] = useState('');
    const [stockQty, setStockQty] = useState('');

    const [itemCategoryDialogVisible, setItemCategoryDialogVisible] = useState(false);
    const [itemNameDialogVisible, setItemNameDialogVisible] = useState(false);
    const [itemCategoryList, setItemCategoryList] = useState([]);
    const [itemNameList, setItemNameList] = useState([]);

    const fetchItemCategories = async () => {
        setLoading(true);
        let result;
        resetItemSelection();
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        result = await itemCategoryAPI(loginBranchID, bearerToken);
        if (result.success) {
            setItemCategoryList(result.categories);
            setItemCategoryDialogVisible(true);
        }
        else {
            Alert.alert("Error", result.message);
        }
        setLoading(false);
    }

    const handleCategorySelect = (itemCategory) => {
        setItemCategoryDialogVisible(false);
        setItemCategory(itemCategory.CategoryName);
        setItemCategoryId(itemCategory.ItemCategoryID);
        fetchItemNames(itemCategory.ItemCategoryID);
    }

    const fetchItemNames = async (selectedItemCategoryId) => {
        setLoading(true);
        let result;
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        result = await itemNameAPI(loginBranchID, 'OKBIN', selectedItemCategoryId, bearerToken);
        if (result.success) {
            setItemNameList(result.itemNames);
            setItemNameDialogVisible(true);
        }
        else {
            Alert.alert("Error", result.message);
        }
        setLoading(false);
    }

    const handleNameSelect = (itemNames) => {
        setItemNameDialogVisible(false);
        setItemName(itemNames.ItemName);
        setItemId(itemNames.ItemID);
        setStockQty(itemNames.StockQty);
        setBatchNumber(itemNames.BatchNumber);

        fetchItemDetails(itemNames.ItemID);
        fetchItemPrice(itemNames.ItemID);

        // Auto-focus logic: focus on appropriate text field after item details are fetched
        setTimeout(() => {
            if (sellByWeight) {
                // If item is sold by weight, focus on weight field
                weightInputRef.current?.focus();
            } else {
                // If item is sold by nos, focus on nos field
                nosInputRef.current?.focus();
            }
        }, 500); // Small delay to ensure item details are loaded

    }
    useEffect(() => {
        if (selectedItemBatchNumber && selectedItemName && selectedItemId && stockQty) {
            console.log(selectedItemBatchNumber, selectedItemCategory, selectedItemCategoryId, selectedItemName, selectedItemId, stockQty);
        }
    }, [selectedItemBatchNumber, selectedItemName, selectedItemId, stockQty]);

    const [itemDetailsList, setItemDetailsList] = useState([]);

    const [stockGroupId, setStockGroupId] = useState('');
    const [taxCategoryId, setTaxCategoryId] = useState('');
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);
    const [totalTaxPercent, setTotalTaxPercent] = useState(0);


    const fetchItemDetails = async (selectedItemId) => {
        let result;
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        result = await itemDetailsAPI(selectedItemId, bearerToken);
        if (result.success) {
            const details = result.itemDetails;
            setItemDetailsList(details);
            setStockGroupId(details.StockGroupId || '');

            setTaxCategoryId(details.TaxCategoryId || '');

            setBatchEnabled(!!details.BatchEnabled);

            setSellByWeight(!!details.SellByWeight);

            setAltQtyEnabled(!!details.AltQtyEnabled);

            setTotalTaxPercent(details.TotalTaxPercent || 0);

        } else {
            Alert.alert("Error", result.message);
        }
    }

    const [itemDayRate, setItemDayRate] = useState('0');
    const [itemPrice, setItemPrice] = useState('');

    const fetchItemPrice = async (selectedItemId) => {
         const bearerToken = await AsyncStorage.getItem('authToken');
        const selectedBranch = await AsyncStorage.getItem('selectedBranch');
        const parsedBranch = JSON.parse(selectedBranch);
        const loginBranchID = parsedBranch.BranchId;
        const result = await itemPriceAPI(loginBranchID, selectedItemId, bearerToken);

        if (result && typeof result === 'number') {
            setItemDayRate(result.toString());
            setItemPrice(result.toString());
        } else if (result?.success === false) {
            console.error('Failed to fetch item price:', result.message);
            setItemDayRate('0');
            setItemPrice('');
        }
    };

    const resetItemSelection = () => {
        setItemCategory('Category');
        setItemCategoryId('');
        setItemName('Item Type');
        setItemId('');
        setBatchNumber('');
        setStockQty('');
        setNos('');
        setWeight('');
        setItemPrice('');
        setItemDayRate('0');
        setPriceWarning('');
        setNosError('');
        setWeightError('');
        setItemDetailsList([]);
        setStockGroupId('');
        setTaxCategoryId('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setTotalTaxPercent(0);
        setItemCategoryDialogVisible(false);
        setItemNameDialogVisible(false);
        setItemCategoryList([]);
        setItemNameList([]);
        setEditingItemIndex(null);
        setSelectedItemsForDelete([]);
    };


    const [nos, setNos] = useState('');
    const [weight, setWeight] = useState('');
    const [priceWarning, setPriceWarning] = useState('');
    const [nosError, setNosError] = useState('');
    const [weightError, setWeightError] = useState('');

    const totalPrice = (() => {
        const priceNum = parseFloat(itemPrice) || 0;
        const weightNum = parseFloat(weight) || 0;
        const nosNum = parseFloat(nos) || 0;

        let total = 0;

        if (weightNum > 0) {
            total = priceNum * weightNum;
        } else if (nosNum > 0) {
            total = priceNum * nosNum;
        }

        return total.toFixed(2);
    })();

    const [itemTableDetails, setItemTableDetails] = useState([]);
    const [editingItemIndex, setEditingItemIndex] = useState(null);
    const [selectedItemsForDelete, setSelectedItemsForDelete] = useState([]);

    // Function to clear all fields and reload the page
    const handleNewBill = () => {
        // Reset all customer fields
        setCustomerId('');
        setCustomerName('');
        setCustomerMobile('');

        // Reset sale type and business channel
        setSelectedSaleType('RETAIL');
        setSelectedBusinessChannel('POS');

        // Reset delivery mode and trip
        setSelectedDeliveryMode('Takeaway');
        setSelectedTripId('');
        setTripList([]);

        // Reset time slot
        setSelectedTimeSlotId('');

        // Reset field locking
        setFieldsLocked(false);

        // Reset item table
        setItemTableDetails([]);

        // Reset editing state
        setEditingItemIndex(null);
        setSelectedItemsForDelete([]);

        // Reset item selection
        resetItemSelection();

        // Reset selected scroll option
        setSelectedScrollOption(null);
    };

    // Function to handle cancel bill with confirmation
    const handleCancelBill = () => {
        Alert.alert(
            "Cancel Bill",
            "Do you really want to cancel this bill?",
            [
                {
                    text: "No",
                    style: "cancel"
                },
                {
                    text: "Yes",
                    onPress: () => {
                        handleNewBill(); // Reuse the new bill function to clear everything
                    }
                }
            ]
        );
    };

    const addItemToTable = () => {
        const weightVal = parseFloat(weight) || 0;
        const nosVal = parseFloat(nos) || 0;
        const priceVal = parseFloat(itemPrice) || 0;

        // If editing an existing item
        if (editingItemIndex !== null) {
            // Validation: Check if nos or weight exceeds stock quantity
            const stockQtyVal = parseFloat(stockQty) || 0;
            if ((nosVal > 0 && nosVal > stockQtyVal) || (weightVal > 0 && weightVal > stockQtyVal)) {
                Alert.alert("Stock Exceeded", "Quantity/Weight cannot exceed available stock quantity.");
                return;
            }

            // Calculate base total price (without tax/discount)
            const baseTotalPrice = weightVal > 0 ? priceVal * weightVal : priceVal * nosVal;
            const taxAmount = (baseTotalPrice * totalTaxPercent) / 100;
            const finalTotalPrice = baseTotalPrice + taxAmount;

            const updatedItem = {
                ...itemTableDetails[editingItemIndex],
                price: priceVal,
                weight: weightVal,
                quantity: nosVal,
                totalPrice: baseTotalPrice.toFixed(2),
                taxAmount: taxAmount.toFixed(2),
                finalTotalPrice: finalTotalPrice.toFixed(2),
            };

            const updatedItems = [...itemTableDetails];
            updatedItems[editingItemIndex] = updatedItem;
            setItemTableDetails(updatedItems);
            setEditingItemIndex(null);
            resetItemSelection();
            return;
        }

        // Validation 3: Check if same item already exists in table
        const existingItem = itemTableDetails.find(item =>
            item.itemId === selectedItemId &&
            item.batchNumber === selectedItemBatchNumber
        );

        if (existingItem) {
            Alert.alert("Duplicate Item", "This item is already added to the table. Please select a different item.");
            return;
        }

        // Validation 4: Check if nos or weight exceeds stock quantity
        const stockQtyVal = parseFloat(stockQty) || 0;
        if ((nosVal > 0 && nosVal > stockQtyVal) || (weightVal > 0 && weightVal > stockQtyVal)) {
            Alert.alert("Stock Exceeded", "Quantity/Weight cannot exceed available stock quantity.");
            return;
        }

        // Calculate base total price (without tax/discount)
        const baseTotalPrice = weightVal > 0 ? priceVal * weightVal : priceVal * nosVal;
        const taxAmount = (baseTotalPrice * totalTaxPercent) / 100;
        const finalTotalPrice = baseTotalPrice + taxAmount;

        const newItem = {
            itemId: selectedItemId,
            itemName: selectedItemName,
            itemCategory: selectedItemCategory,
            itemCategoryId: selectedItemCategoryId,
            batchNumber: selectedItemBatchNumber || '',
            price: priceVal,
            itemDayRate: parseFloat(itemDayRate),
            weight: weightVal,
            quantity: nosVal,
            stockQty: parseFloat(stockQty),
            totalPrice: baseTotalPrice.toFixed(2), // Price * Qty/Weight
            taxPercent: totalTaxPercent || 0,
            taxAmount: taxAmount.toFixed(2),
            finalTotalPrice: finalTotalPrice.toFixed(2), // Base + Tax
            discount: 0,

            // Additional Details
            stockGroupId: stockGroupId || '',
            taxCategoryId: taxCategoryId || '',
            batchEnabled: batchEnabled,
            sellByWeight: sellByWeight,
            altQtyEnabled: altQtyEnabled,
        };

        setItemTableDetails((prev) => [...prev, newItem]);
        resetItemSelection();
    };

    // Function to handle item row click for editing
    const handleItemRowClick = (item, index) => {
        setEditingItemIndex(index);
        setItemCategory(item.itemCategory);
        setItemCategoryId(item.itemCategoryId);
        setItemName(item.itemName);
        setItemId(item.itemId);
        setBatchNumber(item.batchNumber);
        setStockQty(item.stockQty.toString());
        setNos(item.quantity.toString());
        setWeight(item.weight.toString());
        setItemPrice(item.price.toString());
        setItemDayRate(item.itemDayRate.toString());

        // Set item details
        setStockGroupId(item.stockGroupId);
        setTaxCategoryId(item.taxCategoryId);
        setBatchEnabled(item.batchEnabled);
        setSellByWeight(item.sellByWeight);
        setAltQtyEnabled(item.altQtyEnabled);
        setTotalTaxPercent(item.taxPercent);
    };

    // Function to handle checkbox selection for deletion
    const handleItemSelection = (index) => {
        setSelectedItemsForDelete(prev => {
            if (prev.includes(index)) {
                return prev.filter(i => i !== index);
            } else {
                return [...prev, index];
            }
        });
    };

    // Function to handle select all/deselect all
    const handleSelectAll = () => {
        if (selectedItemsForDelete.length === itemTableDetails.length) {
            // If all items are selected, deselect all
            setSelectedItemsForDelete([]);
        } else {
            // Select all items
            setSelectedItemsForDelete(itemTableDetails.map((_, index) => index));
        }
    };

    // Function to delete selected items
    const handleDeleteSelectedItems = () => {
        if (selectedItemsForDelete.length === 0) {
            Alert.alert("No Selection", "Please select items to delete.");
            return;
        }

        Alert.alert(
            "Confirm Delete",
            `Are you sure you want to delete ${selectedItemsForDelete.length} item(s)?`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "Delete",
                    style: "destructive",
                    onPress: () => {
                        // Sort indices in descending order to avoid index shifting issues
                        const sortedIndices = selectedItemsForDelete.sort((a, b) => b - a);

                        setItemTableDetails(prev => {
                            let updatedItems = [...prev];
                            sortedIndices.forEach(index => {
                                updatedItems.splice(index, 1);
                            });
                            return updatedItems;
                        });

                        // Clear selection and editing state
                        setSelectedItemsForDelete([]);
                        setEditingItemIndex(null);
                        resetItemSelection();
                    }
                }
            ]
        );
    };


    const summary = itemTableDetails.reduce((acc, item) => {
        acc.kg += item.weight;
        acc.nos += item.quantity;
        acc.amount += parseFloat(item.totalPrice);
        acc.tax += parseFloat(item.taxAmount);
        acc.discount += item.discount || 0;
        return acc;
    }, { kg: 0, nos: 0, amount: 0, tax: 0, discount: 0 });

    const total = Math.round(summary.amount + summary.tax - summary.discount);







    return (
        <View style={styles.container}>
            {/* App Bar */}
            <Navbar />

            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>
                <View style={styles.scrollOptions_row}>
                    {['New', 'Save', 'View', 'Cancel Bill'].map((option, index) => {
                        const isScrollButtonSelected = selectedScrollOption === option;

                        let buttonStyle = [styles.scrollOptions_button];

                        if (option === 'Cancel Bill') {
                            buttonStyle.push({
                                backgroundColor: isScrollButtonSelected ? '#FE0000' : '#FF3333',
                            });
                        } else if (option === 'Save') {
                            // Check if save should be disabled
                            const isSaveDisabled = !customerMobile || !customerName || itemTableDetails.length === 0;
                            buttonStyle.push({
                                backgroundColor: isSaveDisabled ? '#A0A0A0' : (isScrollButtonSelected ? '#02720F' : '#02A515'),
                            });
                        } else {
                            buttonStyle.push({
                                backgroundColor: isScrollButtonSelected ? '#02096A' : '#DEDDDD',
                            });
                        }

                        return (
                            <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                <TouchableOpacity
                                    style={[
                                        buttonStyle,
                                        // Validation 5: Disable Save button if phone/name not filled or table empty
                                        (option === 'Save' && (!customerMobile || !customerName || itemTableDetails.length === 0)) && {
                                            opacity: 0.3
                                        }
                                    ]}
                                    onPress={async () => {
                                        setSelectedScrollOption(option);

                                        if (option === 'Save') {
                                            // Validation: Check if phone and name are filled and table is not empty
                                            if (!customerMobile || !customerName) {
                                                Alert.alert("Validation Error", "Please enter phone number and name before saving.");
                                                return;
                                            }
                                            if (itemTableDetails.length === 0) {
                                                Alert.alert("Validation Error", "Please add at least one item to the table before saving.");
                                                return;
                                            }

                                            // If customer ID is empty, create customer first
                                            if (!customerId) {
                                                setLoading(true);
                                                try {
                                                    const createResult = await createCustomer(customerName, customerMobile);
                                                    if (createResult.success) {
                                                        // Fetch the created customer to get the customer ID
                                                        const fetchResult = await fetchCustomerByPhone(customerMobile);
                                                        if (fetchResult.success) {
                                                            // Navigate with the new customer data
                                                            navigation.navigate('BillingPayment', {
                                                                customerId: fetchResult.customer.customerCode,
                                                                customerName: fetchResult.customer.customerName,
                                                                customerMobile: fetchResult.customer.mobile,
                                                                selectedSaleType,
                                                                selectedBusinessChannel,
                                                                selectedDeliveryMode,
                                                                selectedTripId,
                                                                selectedTimeSlotId,
                                                                itemTableDetails,
                                                                summary,
                                                                total,
                                                            });
                                                        } else {
                                                            Alert.alert("Error", "Failed to fetch customer after creation.");
                                                        }
                                                    } else {
                                                        Alert.alert("Error", createResult.message || "Failed to create customer.");
                                                    }
                                                } catch (error) {
                                                    console.error('Error in customer creation:', error);
                                                    Alert.alert("Error", "Something went wrong while creating customer.");
                                                } finally {
                                                    setLoading(false);
                                                }
                                            } else {
                                                // Customer already exists, proceed normally
                                                navigation.navigate('BillingPayment', {
                                                    customerId,
                                                    customerName,
                                                    customerMobile,
                                                    selectedSaleType,
                                                    selectedBusinessChannel,
                                                    selectedDeliveryMode,
                                                    selectedTripId,
                                                    selectedTimeSlotId,
                                                    itemTableDetails,
                                                    summary,
                                                    total,
                                                });
                                            }
                                        } else if (option === 'View') {
                                            await openBillModal(); // trigger the popup
                                        } else if (option === 'New') {
                                            // Validation 6: Handle New button
                                            handleNewBill();
                                        } else if (option === 'Cancel Bill') {
                                            // Validation 7: Handle Cancel Bill button
                                            handleCancelBill();
                                        }
                                    }}
                                    disabled={option === 'Save' && (!customerMobile || !customerName || itemTableDetails.length === 0)}
                                >
                                    <Text style={[
                                        styles.scrollOptions_buttonText,
                                        { color: isScrollButtonSelected ? 'white' : 'black' },
                                    ]}>
                                        {option}
                                    </Text>
                                </TouchableOpacity>

                            </View>
                        );
                    })}
                </View>
            </View>

            <Modal
                animationType="slide"
                transparent={true}
                visible={billModalVisible}
                onRequestClose={() => setBillModalVisible(false)}
            >
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                    }}
                >
                    <View
                        style={{
                            backgroundColor: '#fff',
                            borderRadius: 10,
                            padding: 20,
                            width: '90%',
                            maxHeight: '80%',
                        }}
                    >
                        {/* Search, From Date, and To Date in same row */}
                        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10, gap: 8 }}>
                            <TextInput
                                placeholder="Search..."
                                value={searchQuery}
                                onChangeText={(text) => {
                                    setSearchQuery(text);
                                    const filtered = salesList.filter((sale) =>
                                        (sale.DocName || '').toLowerCase().includes(text.toLowerCase())
                                    );
                                    // Sort filtered results by SaleId in increasing order
                                    const sortedFiltered = filtered.sort((a, b) => {
                                        const saleIdA = parseInt(a.SaleId) || 0;
                                        const saleIdB = parseInt(b.SaleId) || 0;
                                        return saleIdA - saleIdB;
                                    });
                                    setFilteredSales(sortedFiltered);
                                }}
                                style={{
                                    flex: 1,
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    height: 40,
                                }}
                            />

                            {/* From Date */}
                            <TouchableOpacity
                                onPress={() => setShowFromDatePicker(true)}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    justifyContent: 'center',
                                    flex: 1,
                                }}
                            >
                                <Text style={{ fontSize: 12, color: '#666' }}>From Date</Text>
                                <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                    {fromDate ? fromDate.toLocaleDateString() : 'Select Date'}
                                </Text>
                            </TouchableOpacity>

                            {/* To Date */}
                            <TouchableOpacity
                                onPress={() => setShowToDatePicker(true)}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    justifyContent: 'center',
                                    flex: 1,
                                }}
                            >
                                <Text style={{ fontSize: 12, color: '#666' }}>To Date</Text>
                                <Text style={{ fontSize: 14, fontWeight: '500' }}>
                                    {toDate ? toDate.toLocaleDateString() : 'Select Date'}
                                </Text>
                            </TouchableOpacity>
                        </View>

                        {/* From Date Picker */}
                        {showFromDatePicker && (
                            <DateTimePicker
                                value={fromDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, date) => {
                                    setShowFromDatePicker(false);
                                    if (date) {
                                        setFromDate(date);
                                        // Only auto-set toDate if user hasn't picked one yet
                                        if (!toDate) {
                                            const nextDay = new Date(date);
                                            nextDay.setDate(nextDay.getDate() + 1);
                                            setToDate(nextDay);
                                        }
                                    }
                                }}
                            />
                        )}

                        {/* To Date Picker */}
                        {showToDatePicker && (
                            <DateTimePicker
                                value={toDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, date) => {
                                    setShowToDatePicker(false);
                                    if (date) {
                                        if (fromDate && date.toDateString() === fromDate.toDateString()) {
                                            Alert.alert("Date Error", "From Date and To Date cannot be the same.");
                                        } else {
                                            setToDate(date);
                                        }
                                    }
                                }}
                            />
                        )}

                        {/* Bills List - 3 in a row, big squared buttons */}
                        <ScrollView
                            contentContainerStyle={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                justifyContent: 'space-between',
                                paddingHorizontal: 5,
                            }}
                        >
                            {filteredSales.map((sale, index) => (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => {
                                        handleBillSelect(sale.SaleId);
                                        setBillModalVisible(false);
                                    }}
                                    style={{
                                        backgroundColor: '#FDC500',
                                        padding: 15,
                                        borderRadius: 12,
                                        width: '31%',
                                        aspectRatio: 1,
                                        marginBottom: 15,
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.25,
                                        shadowRadius: 3.84,
                                        elevation: 5,
                                    }}
                                >
                                    <Text style={{
                                        color: '#000',
                                        textAlign: 'center',
                                        fontWeight: 'bold',
                                        fontSize: 16,
                                        marginBottom: 8,
                                    }}>
                                        {sale.DocName}
                                    </Text>
                                 
                                </TouchableOpacity>
                            ))}
                        </ScrollView>

                        {/* Back Button */}
                        <TouchableOpacity onPress={() => setBillModalVisible(false)}>
                            <Text style={{ marginTop: 20, textAlign: 'center', color: '#000' }}>Back</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>




            <View style={styles.saleBusinessContainer}>
                {/* Sale Type */}
                <View style={styles.saleType_container}>
                    <View style={styles.saleType_row}>
                        {['RETAIL', 'HORECA'].map((type) => (
                            <TouchableOpacity
                                key={type}
                                onPress={() => {
                                    // Validation 2: Disable sale type when items are in table or fields are locked
                                    if (itemTableDetails.length > 0 || fieldsLocked) {
                                        Alert.alert("Cannot Change", "Sale type cannot be changed when items are added to the table or category is selected.");
                                        return;
                                    }
                                    setSelectedSaleType(type);
                                }}
                                style={[
                                    styles.saleType_button,
                                    {
                                        backgroundColor:
                                            selectedSaleType == type ? '#02096A' : '#D3D3D3',
                                        flex: 1,
                                        // Validation 2: Visual indication when disabled
                                        opacity: (itemTableDetails.length > 0 || fieldsLocked) ? 0.5 : 1,
                                    },
                                ]}
                                disabled={itemTableDetails.length > 0 || fieldsLocked}
                            >
                                <Text
                                    style={[
                                        styles.saleType_text,
                                        {
                                            color: selectedSaleType == type ? 'white' : 'black',
                                        },
                                    ]}
                                >
                                    {type}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>

                {/* Business Channel */}
                <View style={styles.businessChannel_container}>
                    <View style={styles.businessChannel_row}>
                        {['POS', 'WEB'].map((button) => (
                            <TouchableOpacity
                                key={button}
                                onPress={() => {
                                    // Validation 2: Disable business channel when items are in table or fields are locked
                                    if (itemTableDetails.length > 0 || fieldsLocked) {
                                        Alert.alert("Cannot Change", "Business channel cannot be changed when items are added to the table or category is selected.");
                                        return;
                                    }
                                    setSelectedBusinessChannel(button);
                                }}
                                style={[
                                    styles.businessChannel_button,
                                    {
                                        backgroundColor:
                                            selectedBusinessChannel == button ? '#02096A' : '#D3D3D3',
                                        flex: 1,
                                        // Validation 2: Visual indication when disabled
                                        opacity: (itemTableDetails.length > 0 || fieldsLocked) ? 0.5 : 1,
                                    },
                                ]}
                                disabled={itemTableDetails.length > 0 || fieldsLocked}
                            >
                                <Text
                                    style={[
                                        styles.businessChannel_text,
                                        {
                                            color:
                                                selectedBusinessChannel == button ? 'white' : 'black',
                                        },
                                    ]}
                                >
                                    {button}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </View>


            {/* Customer Details */}
            <View style={styles.customer_container}>
                {/* Row 1: Customer ID, Phone, Name */}
                <View style={styles.customer_row}>
                    <View style={styles.customer_phoneField}>
                        <TextInput
                            placeholder="Customer ID"
                            style={[
                                styles.customer_textfield,
                                { color: '#888', backgroundColor: '#f0f0f0' }
                            ]}
                            value={customerId}
                            onChangeText={setCustomerId}
                            editable={false}
                        />
                    </View>

                    <View style={[
                        styles.customer_phoneField,
                        (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5 }
                    ]}>
                        <TextInput
                            placeholder="Phone"
                            keyboardType="numeric"
                            style={styles.customer_textfield}
                            value={customerMobile}
                            onChangeText={(text) => {
                                setCustomerMobile(text);
                                if (text.length === 10) {
                                    fetchCustomers(text);
                                }
                            }}
                            maxLength={10}
                            editable={itemTableDetails.length === 0 && !fieldsLocked}
                        />
                    </View>

                    <View style={[
                        styles.customer_nameField,
                        (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5 }
                    ]}>
                        <TextInput
                            placeholder="Name"
                            style={styles.customer_textfield}
                            value={customerName}
                            onChangeText={setCustomerName}
                            editable={itemTableDetails.length === 0 && !fieldsLocked}
                        />
                    </View>



                    <TouchableOpacity
                        style={[
                            styles.customer_button,
                            (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5, backgroundColor: '#A0A0A0' }
                        ]}
                        onPress={() => fetchCustomers(customerMobile)}
                        disabled={itemTableDetails.length > 0 || fieldsLocked}
                    >
                        <Text style={styles.customer_buttonText}>Search</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 2: Delivery Mode, Trip ID, and Time Slot */}
                <View style={[styles.customer_row, { marginTop: 12 }]}>
                    <View style={[
                        styles.customer_deliveryField,
                        (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5 }
                    ]}>
                        <Dropdown
                            data={[
                                { label: 'Takeaway', value: 'Takeaway' },
                                { label: 'Home delivery', value: 'Home delivery' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Delivery Mode"
                            value={selectedDeliveryMode}
                            onChange={(item) => handleDeliveryModeChange(item.value)}
                            style={styles.customer_dropdown}
                            placeholderStyle={styles.customer_dropdownPlaceholder}
                            selectedTextStyle={styles.customer_dropdownText}
                            disable={itemTableDetails.length > 0 || fieldsLocked}
                        />
                    </View>

                    <View style={[
                        styles.customer_tripField,
                        (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5 }
                    ]}>
                        <Dropdown
                            data={tripList.map(trip => ({
                                label: trip.tripID,
                                value: trip.tripID,
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Trip ID"
                            value={selectedTripId}
                            onChange={(item) => setSelectedTripId(item.value)}
                            style={[
                                styles.customer_dropdown,
                                {
                                    backgroundColor: selectedDeliveryMode === 'Home delivery' ? '#ffffff' : '#e0e0e0'
                                }
                            ]}
                            placeholderStyle={styles.customer_dropdownPlaceholder}
                            selectedTextStyle={styles.customer_dropdownText}
                            disable={selectedDeliveryMode !== 'Home delivery' || itemTableDetails.length > 0 || fieldsLocked}
                        />
                        {tripLoading && (
                            <ActivityIndicator
                                size="small"
                                color="#02096A"
                                style={{ position: 'absolute', right: 10, top: 15 }}
                            />
                        )}
                    </View>

                    <View style={[
                        styles.customer_tripField,
                        (itemTableDetails.length > 0 || fieldsLocked) && { opacity: 0.5 }
                    ]}>
                        <Dropdown
                            data={timeSlotList.map(slot => ({
                                label: slot.TimeSlotName,
                                value: slot.TimeSlotID,
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Time Slot"
                            value={selectedTimeSlotId}
                            onChange={(item) => setSelectedTimeSlotId(item.value)}
                            style={styles.customer_dropdown}
                            placeholderStyle={styles.customer_dropdownPlaceholder}
                            selectedTextStyle={styles.customer_dropdownText}
                            disable={itemTableDetails.length > 0 || fieldsLocked}
                        />
                        {timeSlotLoading && (
                            <ActivityIndicator
                                size="small"
                                color="#02096A"
                                style={{ position: 'absolute', right: 10, top: 15 }}
                            />
                        )}
                    </View>
                </View>
            </View>

            {/* customer search pop up */}

            <Modal visible={customerDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a Customer</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {customerList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No customer found.
                                </Text>
                            ) : (
                                customerList.map((cust, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            backgroundColor: '#EFEFEF',
                                            padding: 10,
                                            marginVertical: 6,
                                            borderRadius: 6,
                                            alignItems: 'center',
                                        }}
                                        onPress={() => handleCustomerSelect(cust)}
                                    >
                                        <Text style={{
                                            textAlign: 'center',
                                            fontSize: 12,
                                        }}>{cust.customerName}</Text>
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => setCustomerDialogVisible(false)}
                            style={{
                                marginTop: 10,
                                backgroundColor: '#FF3B30',
                                padding: 10,
                                borderRadius: 8,
                                alignSelf: 'center',
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {loading && (
                <View style={{
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                    right: 0,
                    left: 0,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#ffffffcc',
                }}>
                    <ActivityIndicator size="large" color="#0000ff" />
                </View>
            )}

            {/* item search */}
            <Modal visible={itemCategoryDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a category</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {itemCategoryList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No category found.
                                </Text>
                            ) : (
                                itemCategoryList.map((itemCategory, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            height: 100,
                                            backgroundColor: '#EFEFEF',
                                            padding: 15,
                                            marginVertical: 8,
                                            marginHorizontal: '1.5%',
                                            borderRadius: 12,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.25,
                                            shadowRadius: 3.84,
                                            elevation: 5,
                                        }}
                                        onPress={() => handleCategorySelect(itemCategory)}
                                    >
                                        <Text style={{
                                            textAlign: 'center',
                                            fontSize: 14,
                                            fontWeight: 'bold',
                                            color: '#333',
                                        }}>{itemCategory.CategoryName}</Text>
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => setItemCategoryDialogVisible(false)}
                            style={{
                                marginTop: 20,
                                backgroundColor: '#FF3B30',
                                paddingVertical: 15,
                                paddingHorizontal: 30,
                                borderRadius: 10,
                                alignSelf: 'center',
                                minWidth: 120,
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: 16,
                                textAlign: 'center',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>


            <Modal visible={itemNameDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a item type</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {itemNameList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No stock available
                                </Text>
                            ) : (
                                itemNameList.map((itemNames, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            height: 100,
                                            backgroundColor: '#EFEFEF',
                                            padding: 12,
                                            marginVertical: 8,
                                            marginHorizontal: '1.5%',
                                            borderRadius: 12,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            shadowColor: '#000',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.25,
                                            shadowRadius: 3.84,
                                            elevation: 5,
                                        }}
                                        onPress={() => handleNameSelect(itemNames)}
                                    >
                                        {itemNames.BatchNumber ? (
                                            <>
                                                <Text style={{
                                                    textAlign: 'center',
                                                    fontSize: 11,
                                                    fontWeight: 'bold',
                                                    color: '#666',
                                                    marginBottom: 4,
                                                }}>{itemNames.BatchNumber}</Text>
                                                <Text style={{
                                                    textAlign: 'center',
                                                    fontSize: 12,
                                                    fontWeight: 'bold',
                                                    color: '#333',
                                                }}>{itemNames.ItemName}</Text>
                                            </>
                                        ) : (
                                            <Text style={{
                                                textAlign: 'center',
                                                fontSize: 12,
                                                fontWeight: 'bold',
                                                color: '#333',
                                            }}>{itemNames.ItemName}</Text>
                                        )}
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => {
                                setItemNameDialogVisible(false);
                                fetchItemCategories();
                            }}
                            style={{
                                marginTop: 20,
                                backgroundColor: '#FF3B30',
                                paddingVertical: 15,
                                paddingHorizontal: 30,
                                borderRadius: 10,
                                alignSelf: 'center',
                                minWidth: 120,
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                                fontSize: 16,
                                textAlign: 'center',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>


            {/* Item Details */}
            <View style={styles.item_container}>

                {/* Row 1: Category and Item Type */}
                <View style={styles.item_row1}>
                    <View style={styles.item_chooseBox}>
                        <TouchableOpacity
                            style={[
                                styles.item_chooseButton,
                                // Validation 1: Disable Choose Category when phone and name are not filled
                                (!customerMobile || !customerName) && { opacity: 0.5 }
                            ]}
                            onPress={() => {
                                // Validation 1: Check if phone and name are filled
                                if (!customerMobile || !customerName) {
                                    Alert.alert("Validation Error", "Please enter phone number and name before choosing category.");
                                    return;
                                }
                                // Lock fields when category selection starts
                                setFieldsLocked(true);
                                fetchItemCategories();
                            }}
                            disabled={!customerMobile || !customerName}
                        >
                            <Text style={styles.item_actionButtonText}>Choose Category</Text>
                        </TouchableOpacity>
                    </View>

                    <Text style={styles.item_titleText}>
                        {selectedItemCategory + '/ ' + selectedItemName}
                    </Text>
                </View>


                {/* Row 2: Batch No, Nos, Weight */}
                <View style={styles.item_row2}>
                    <View style={{ flex: 1, marginRight: 8 }}>
                        <TextInput
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: batchEnabled ? '#f0f0f0' : '#e0e0e0' }
                            ]}
                            placeholder="Batch No."
                            keyboardType="numeric"
                            value={selectedItemBatchNumber}
                            onChangeText={() => { }}
                            editable={false}
                        />

                    </View>
                    <View style={{ flex: 2, marginHorizontal: 8 }}>
                        <TextInput
                            ref={nosInputRef}
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: (altQtyEnabled || !sellByWeight) ? '#ffffff' : '#e0e0e0' }
                            ]}
                            placeholder="Nos"
                            keyboardType="numeric"
                            value={nos}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,2}$/.test(text);
                                if (valid) {
                                    setNos(text); // Always allow updating the field if valid number format
                                    if (text === '') {
                                        setNosError('');
                                    } else if (parseFloat(text) > parseFloat(stockQty)) {
                                        setNosError(`Exceeds stock quantity (${stockQty})`);
                                    } else {
                                        setNosError('');
                                    }
                                }
                            }}

                            editable={altQtyEnabled || !sellByWeight}
                        />
                        {nosError ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{nosError}</Text>
                        ) : null}
                    </View>

                    <View style={{ flex: 2, marginLeft: 8 }}>
                        <TextInput
                            ref={weightInputRef}
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: (altQtyEnabled || sellByWeight) ? '#ffffff' : '#e0e0e0' }
                            ]}
                            placeholder="Weight (kg)"
                            keyboardType="numeric"
                            value={weight}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,3}$/.test(text);
                                if (text === '' || valid) {
                                    setWeight(text); // Allow empty or valid input
                                    if (text === '') {
                                        setWeightError('');
                                    } else if (parseFloat(text) > parseFloat(stockQty)) {
                                        setWeightError(`Exceeds stock quantity (${stockQty})`);
                                    } else {
                                        setWeightError('');
                                    }
                                }
                            }}

                            editable={altQtyEnabled || sellByWeight}
                        />
                        {weightError ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{weightError}</Text>
                        ) : null}
                    </View>

                </View>

                {/* Row 3: Discount Dropdown, Price per Unit, Discount % */}
                <View style={styles.item_row3}>
                    <View style={{ flex: 1, marginRight: 8 }}>
                        <Dropdown
                            data={[
                                { label: 'Discount on', value: 'Discount on' },
                                { label: 'Total value', value: 'Total value' },
                                { label: 'Total kg', value: 'Total kg' },
                                { label: 'Total percentage', value: 'Total percentage' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Discount"
                            editable={false}
                            disabled={true}
                            value=''
                            onChange={() => { }}
                            style={styles.item_dropdown}
                        />
                    </View>
                    <View style={{ flex: 1, marginHorizontal: 8 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Price per Unit"
                            keyboardType="decimal-pad"
                            value={itemPrice}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,2}$/.test(text);
                                if (text === '' || valid) {
                                    setItemPrice(text); // Allow empty or valid input
                                    if (text === '') {
                                        setPriceWarning('');
                                    } else if (parseFloat(text) < parseFloat(itemDayRate)) {
                                        setPriceWarning(`Less than ₹${itemDayRate}`);
                                    } else {
                                        setPriceWarning('');
                                    }
                                }
                            }}

                        />
                        {priceWarning ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{priceWarning}</Text>
                        ) : null}

                    </View>
                    <View style={{ flex: 1, marginLeft: 8 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Discount %"
                            editable={false}
                            keyboardType="decimal-pad"
                        />
                    </View>
                </View>

                {/* Row 4: Remarks */}
                <View style={styles.item_row4}>
                    <View style={{ flex: 1 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Remarks"
                        />
                    </View>
                </View>

                {/* Row 5: Total Price and Buttons */}
                <View style={styles.item_row5}>
                    <View style={[styles.item_totalPriceContainer, { flex: 1 }]}>
                        <Text style={styles.item_totalPriceText}>
                            Total Price: ₹{totalPrice}
                        </Text>
                    </View>


                    <TouchableOpacity
                        style={[
                            styles.item_actionButton,
                            { flex: 1 },
                            // Disable Add button if validation fails
                            (!selectedItemId || (!nos && !weight) || !!nosError || !!weightError) && { opacity: 0.5 }
                        ]}
                        onPress={() => {
                            // Additional validation before adding
                            if (!selectedItemId) {
                                Alert.alert("Item Required", "Please select an item first.");
                                return;
                            }
                            if (!nos && !weight) {
                                Alert.alert("Quantity Required", "Please enter either quantity (Nos) or weight.");
                                return;
                            }
                            if (nosError || weightError) {
                                Alert.alert("Validation Error", "Please fix the quantity/weight errors before adding.");
                                return;
                            }
                            addItemToTable();
                        }}
                        disabled={!selectedItemId || (!nos && !weight) || !!nosError || !!weightError}
                    >
                        <Text style={styles.item_actionButtonText}>
                            {editingItemIndex !== null ? 'Update' : 'Add'}
                        </Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.item_actionButton, { flex: 1 }]}
                        onPress={() => {
                            setEditingItemIndex(null);
                            resetItemSelection();
                        }}
                    >
                        <Text style={styles.item_actionButtonText}>Clear</Text>
                    </TouchableOpacity>
                </View>

            </View>


            {/* Table */}
            <View style={styles.table_container}>
                <ScrollView horizontal>
                    <View style={{ width: Dimensions.get('window').width }}>
                        <ScrollView>
                            <DataTable>
                                <DataTable.Header style={styles.table_header}>
                                    <DataTable.Title>
                                        <TouchableOpacity onPress={handleSelectAll}>
                                            <Text style={{ color: 'white', fontSize: 16 }}>
                                                {selectedItemsForDelete.length === itemTableDetails.length && itemTableDetails.length > 0 ? '☑' : '☐'}
                                            </Text>
                                        </TouchableOpacity>
                                    </DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>BatchNo</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Item Name</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Price</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Weight (kg)</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Quantity</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Total Price</Text></DataTable.Title>
                                </DataTable.Header>

                                {/* Replace this block with dynamic rows later */}
                                {itemTableDetails.map((item, index) => (
                                    <DataTable.Row
                                        key={index}
                                        onPress={() => handleItemRowClick(item, index)}
                                        style={{
                                            backgroundColor: editingItemIndex === index ? '#E3F2FD' :
                                                           selectedItemsForDelete.includes(index) ? '#FFEBEE' : 'transparent'
                                        }}
                                    >
                                        <DataTable.Cell>
                                            <TouchableOpacity onPress={() => handleItemSelection(index)}>
                                                <Text style={{ fontSize: 16 }}>
                                                    {selectedItemsForDelete.includes(index) ? '☑' : '☐'}
                                                </Text>
                                            </TouchableOpacity>
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            {item.batchNumber}
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            {item.itemName}
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            ₹{item.price.toFixed(2)}
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            {item.weight.toFixed(3)}
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            {item.quantity.toFixed(2)}
                                        </DataTable.Cell>
                                        <DataTable.Cell onPress={() => handleItemRowClick(item, index)}>
                                            ₹{item.totalPrice}
                                        </DataTable.Cell>
                                    </DataTable.Row>
                                ))}

                            </DataTable>
                        </ScrollView>
                    </View>
                </ScrollView>
            </View>

            <View style={styles.summary_row}>
                <View style={styles.summary_box}>
                    <View style={styles.summary_detailRow}>
                        <Text style={styles.summary_label}>Kg:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>{summary.kg.toFixed(3)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Nos:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>{summary.nos.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Amount:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.amount.toFixed(2)}</Text>
                        </View>
                    </View>

                    <View style={styles.summary_detailRow}>
                        <Text style={styles.summary_label}>Tax:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.tax.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Discount:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.discount.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Total:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{total.toFixed(2)}</Text>
                        </View>
                    </View>
                </View>

                <TouchableOpacity
                    style={[
                        styles.summary_deleteButton,
                        selectedItemsForDelete.length === 0 && { opacity: 0.5 }
                    ]}
                    onPress={handleDeleteSelectedItems}
                    disabled={selectedItemsForDelete.length === 0}
                >
                    <Text style={{ color: 'white', fontWeight: 'bold' }}>
                        Delete {selectedItemsForDelete.length > 0 ? `(${selectedItemsForDelete.length})` : ''}
                    </Text>
                </TouchableOpacity>
            </View>

        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== DIALOG STYLES ====================
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 16,
        width: '80%',
        maxHeight: '80%',
    },
    dialog_title: {
        fontSize: 15,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
    },
    dialog_content: {
        maxHeight: Dimensions.get('window').height * 0.6,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start', // Changed from space-between to flex-start
        gap: 10,
    },
    dialog_button: {
        backgroundColor: '#FDC500',
        borderRadius: 8,
        padding: 10,
        width: '23%',
        minHeight: 100, // Set minimum height instead of aspect ratio
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 10,
        marginRight: '2%', // Add margin to ensure proper spacing
    },
    dialog_buttonText: {
        fontSize: 14,
        color: 'rgb(2, 2, 2)',
        textAlign: 'center',
    },
    dialog_batchText: {
        fontSize: 12,
        color: '#02096A',
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 4,
    },
    dialog_backButton: {
        marginTop: 18,
        alignSelf: 'center',
    },
    dialog_backButtonText: {
        color: 'black',
        fontSize: 15,
    },
    // Loading styles
    loadingContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 14,
        color: '#333',
    },
    fullScreenLoading: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================  appbar
    menu_row: {
        paddingHorizontal: 8,
        alignItems: 'center',
        gap: 8,
    },
    menu_button: {
        paddingHorizontal: 12,
        paddingVertical: 8,
        marginHorizontal: 4,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================   new
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'wrap',
        paddingHorizontal: 8,
        gap: 8,
    },
    //NEW
    scrollOptions_buttonWrapper: {
        flex: 1,
        marginHorizontal: 4,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        borderWidth: 1.5,
        borderColor: 'transparent',
    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== SALE TYPES & BUSINESS CHANNELS CONTAINER ====================
    saleBusinessContainer: {
        flexDirection: 'row',
        backgroundColor: '#E6E6E6',
        padding: 8,
    },

    // ==================== SALE TYPES STYLES ====================   retail / horeca
    saleType_container: {
        flex: 1,
        paddingLeft: 4,
    },
    saleType_row: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        backgroundColor: '#F0F0F0',
        borderWidth: 1,
        borderColor: '#041C44',
        borderRadius: 10,
        padding: 5,
    },
    //RETAIL/HORIKA
    saleType_button: {
        marginHorizontal: 5,
        paddingVertical: 12,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    saleType_text: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
    },

    // ==================== BUSINESS CHANNEL STYLES ====================  pos/web
    businessChannel_container: {
        flex: 1,
        paddingRight: 4,
        paddingHorizontal: 8,
    },
    businessChannel_row: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        backgroundColor: '#F0F0F0',
        borderWidth: 1,
        borderColor: '#041C44',
        borderRadius: 10,
        padding: 5,
    },
    //POS/WEB
    businessChannel_button: {
        marginHorizontal: 5,
        paddingVertical: 12,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    businessChannel_text: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
    },

    // ==================== CUSTOMER DETAILS STYLES ====================
    customer_container: {
        padding: 8,
        width: '100%',
    },
    customer_row: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    customer_phoneField: {
        minWidth: 120,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
    },
    customer_nameField: {
        flex: 1,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
        marginHorizontal: 8,
    },
    customer_textfield: {
        fontSize: 14,
        fontWeight: '500',
        flexShrink: 1,
        flexGrow: 1,
        width: '100%',
    },
    customer_button: {
        backgroundColor: '#02096A',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        marginHorizontal: 4,
    },
    customer_buttonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    customer_deliveryField: {
        minWidth: 150,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
        marginRight: 8,
    },
    customer_tripField: {
        flex: 1,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
        position: 'relative',
    },
    customer_dropdown: {
        height: '100%',
        paddingHorizontal: 8,
    },
    customer_dropdownPlaceholder: {
        fontSize: 14,
        color: '#999',
    },
    customer_dropdownText: {
        fontSize: 14,
        fontWeight: '500',
        color: '#000',
    },

    // ==================== ITEM DETAILS STYLES ====================
    item_container: {
        padding: 4,
        width: '100%',
    },
    item_row1: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
        marginBottom: 14,
        flexWrap: 'wrap',
        gap: 8,

    },
    item_row2: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 14,
    },
    item_row3: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 14,
    },
    item_row4: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 8,
    },
    item_row5: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 14,
    },
    item_chooseBox: {
        flex: 1,
        height: 50,
        width: 70,
        marginLeft: 8,
        marginRight: 8,
    },
    item_chooseButton: {
        backgroundColor: '#02096A',
        height: '100%',
        width: '150',
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    item_titleText: {
        fontSize: 28,
        fontWeight: 'bold',
        color: 'red',
        textAlign: 'center',
        marginBottom: 8,
        paddingHorizontal: 8,
        flex: 4,
    },
    item_input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 10,
        paddingHorizontal: 10,
        paddingVertical: 8,
        fontSize: 14,
        height: 55
    },
    item_dropdown: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 10,
        paddingHorizontal: 10,
        paddingVertical: 8,
        height: 55,
        justifyContent: 'center',
    },
    item_totalPriceContainer: {
        padding: 10,
        backgroundColor: 'transparent',
        borderRadius: 10,
        marginHorizontal: 4,
    },
    item_totalPriceText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#000',
    },
    item_actionButton: {
        backgroundColor: '#02096A',
        paddingVertical: 12,
        borderRadius: 9,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 4,
    },
    item_actionButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 14,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: 2,
        marginLeft: 4,
    },

    // ==================== TABLE STYLES ====================
    table_container: {
        width: '100%',
        height: 220,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        marginBottom: 10,
        padding: 8,
    },
    table_header: {
        backgroundColor: 'rgb(2, 9, 106)',
    },
    table_headerText: {
        color: 'white',
        fontWeight: 'bold',
    },

    // ==================== SUMMARY SECTION STYLES ====================
    summary_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 8,
        paddingHorizontal: 16,
        gap: 8,
    },
    summary_deleteButton: {
        backgroundColor: 'red',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        alignSelf: 'center',
        marginLeft: 8,
    },
    summary_box: {
        paddingVertical: 14,
        flex: 1,
    },
    summary_detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        flexWrap: 'wrap',
        gap: 8,
    },
    summary_label: {
        width: 60,
        fontSize: 14,
        fontWeight: 'bold',
        fontFamily: 'Times New Roman',
    },
    summary_valueBox: {
        width: 70,
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderWidth: 1,
        borderColor: '#000',
        borderRadius: 5,
        marginRight: 8,
    },
    summary_valueText: {
        fontSize: 14,
        fontWeight: 'bold',
        fontFamily: 'Times New Roman',
    },
});

export default BillingScreen;
